import 'package:eam/be/FUNCLOC_LOC_ADD.dart';
import 'package:eam/be/FUNCLOC_ORG_ADD.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/tech_objects_helper.dart';
import 'package:eam/utils/utils.dart';
import 'package:eam/widgets/details_card_widget.dart';
import 'package:eam/widgets/label_value_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hexcolor/hexcolor.dart';

import '../../../../../app_styles/app_styles.dart';
import '../../../../../widgets/atoms_layer/general/texts.dart';
import '../../../../../widgets/molecules_layer/general/general_helper_widgets.dart';
import '../../../../../../utils/screen_util.dart';

class FlocDetailGeneral extends StatefulWidget {
  final String flocLoc;

  const FlocDetailGeneral({Key? key, required this.flocLoc}) : super(key: key);

  @override
  _FlocDetailGeneralState createState() => _FlocDetailGeneralState();
}

class _FlocDetailGeneralState extends State<FlocDetailGeneral> {
  FUNCLOC_LOC_ADD? location;
  FUNCLOC_ORG_ADD? org;

  static const double _cardHeight = 45;
  double _itemPadding = 20;
  double _betweenSpace = 24.0;

  final generalDivider = Divider(
    color: HexColor("#D5DADD"),
  );

  @override
  Widget build(BuildContext context) {
    return Container(
      child: FutureBuilder(
        builder:
            (BuildContext context, AsyncSnapshot<FUNC_LOC_HEADER?> snapshot) {
          if (snapshot.hasData) {
            FUNC_LOC_HEADER? funcLocHeader = snapshot.data;
            return SingleChildScrollView(
              child: Container(
                decoration: AppStyles.deFaultBoxDecoration,
                child: Padding(
                  padding: EdgeInsets.only(
                      left: _itemPadding,
                      right: _itemPadding,
                      top: _itemPadding,
                      bottom: _itemPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _getGeneralCard(funcLocHeader),
                      _betweenSpace.spaceY,
                      generalDivider,
                      _betweenSpace.spaceY,
                      _getLocationCard(context, funcLocHeader),
                      _betweenSpace.spaceY,
                      generalDivider,
                      _betweenSpace.spaceY,
                      _getResponsibilityCard(context, funcLocHeader),
                      _betweenSpace.spaceY,
                      generalDivider,
                      _betweenSpace.spaceY,
                      getOrganizationCard(context, org, location),
                    ],
                  ),
                ),
              ),
            );
          } else if (snapshot.hasError) {
            return Center(
              child: Text(snapshot.error.toString()),
            );
          } else {
            return Center(
              child: CircularProgressIndicator(),
            );
          }
        },
        future: _getFlocHeader(funcLoc: widget.flocLoc),
      ),
    );
  }

  _getResponsibilityCard(BuildContext context, FUNC_LOC_HEADER? funcLocHeader) {
    if (PlatformDetails.isMobileScreen(context) ||
        PlatformDetails.isTabPortraitScreen(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GeneralTitleText(
            title: AppLocalizations.of(context)!.responsibility,
          ),
          SizedBox(
            height: 16,
          ),
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.planningPlant,
              subtitle: org != null
                  ? Utils.formatIdDescriptionWithhyphen(
                      id: org!.org_plng_plant,
                      description: org!.org_plng_plant_desc,
                    )
                  : AppLocalizations.of(context)!.na,
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.plannerGroup,
              subtitle: org != null
                  ? Utils.formatIdDescriptionWithhyphen(
                      id: org!.org_plnr_grp,
                      description: org!.org_plnr_grp_desc,
                    )
                  : AppLocalizations.of(context)!.na,
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.mainWorkCenter,
              subtitle: funcLocHeader != null
                  ? Utils.formatIdDescriptionWithhyphen(
                      id: funcLocHeader.org_main_wrk_cntr,
                      description: funcLocHeader.org_main_wrk_cntr_desc,
                    )      
                  : AppLocalizations.of(context)!.na,
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GeneralTitleText(
          title: AppLocalizations.of(context)!.responsibility,
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.planningPlant,
                  subtitle: org != null
                      ? Utils.formatIdDescriptionWithhyphen(
                          id: org!.org_plng_plant,
                          description: org!.org_plng_plant_desc,
                       )
                      : AppLocalizations.of(context)!.na,
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.plannerGroup,
                  subtitle: org != null
                      ? Utils.formatIdDescriptionWithhyphen(
                          id: org!.org_plnr_grp,
                          description: org!.org_plnr_grp_desc,
                        )
                      : AppLocalizations.of(context)!.na,
                ),
              ),
            ),
          ],
        ),
        ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
        SizedBox(
          height: _cardHeight,
          child: GeneralItem(
            title: AppLocalizations.of(context)!.mainWorkCenter,
            subtitle: funcLocHeader != null
                ? Utils.formatIdDescriptionWithhyphen(
                    id: funcLocHeader.org_main_wrk_cntr,
                    description: funcLocHeader.org_main_wrk_cntr_desc,
                  ) 
                : AppLocalizations.of(context)!.na,
          ),
        ),
      ],
    );
  }

  _getLocationCard(BuildContext context, FUNC_LOC_HEADER? funcLocHeader) {
    if (PlatformDetails.isMobileScreen(context) ||
        PlatformDetails.isTabPortraitScreen(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GeneralTitleText(
            title: AppLocalizations.of(context)!.location,
          ),
          SizedBox(
            height: 16,
          ),
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.maintenancePlant,
              // subtitle: location != null
              //     ? funcLocHeader!.loc_main_plant ??
              //         "" + " - " + funcLocHeader.loc_main_plant_desc.toString()
              //     : funcLocHeader!.loc_main_plant ??
              //         "" + " - " + funcLocHeader.loc_main_plant_desc.toString(),
              subtitle: funcLocHeader != null
                  ? Utils.formatIdDescriptionWithhyphen(
                      id: funcLocHeader.loc_main_plant,
                      description: funcLocHeader.loc_main_plant_desc,
                    )
                  : AppLocalizations.of(context)!.na,
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.plantSection,
              subtitle: location != null
                  ? Utils.formatIdDescriptionWithhyphen(
                      id: location?.loc_plant_sec,
                      description: location?.loc_plant_sec_desc,
                    )
                  : AppLocalizations.of(context)!.na,
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GeneralTitleText(
          title: AppLocalizations.of(context)!.location,
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.maintenancePlant,
                  // subtitle: location != null
                  //     ? funcLocHeader!.loc_main_plant! +
                  //         " - " +
                  //         funcLocHeader.loc_main_plant_desc!
                  //     : "",
                  subtitle: funcLocHeader != null
                      ? Utils.formatIdDescriptionWithhyphen(
                          id: funcLocHeader.loc_main_plant,
                          description: funcLocHeader.loc_main_plant_desc,
                        )
                      : AppLocalizations.of(context)!.na,
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.plantSection,
                  subtitle: location != null
                      ? Utils.formatIdDescriptionWithhyphen(
                          id: location?.loc_plant_sec,
                          description: location?.loc_plant_sec_desc,
                        )
                      : AppLocalizations.of(context)!.na,
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  Visibility _getManufacturerCard(BuildContext context) {
    return Visibility(
      visible: false,
      child: DetailsCardWidget(
        isEditable: false,
        title: AppLocalizations.of(context)!.manufacturer,
        content: Container(
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelValueWidget(
                  valueController: TextEditingController(text: ""),
                  label: AppLocalizations.of(context)!.name,
                  isValueEditable: false,
                  isValueReadOnly: true,
                  onValueTap: () {}),
              LabelValueWidget(
                  valueController: TextEditingController(text: ""),
                  label: AppLocalizations.of(context)!.modelNo,
                  isValueEditable: false,
                  isValueReadOnly: true,
                  onValueTap: () {}),
              LabelValueWidget(
                  valueController: TextEditingController(text: ""),
                  label: AppLocalizations.of(context)!.partNo,
                  isValueEditable: false,
                  isValueReadOnly: true,
                  onValueTap: () {}),
              LabelValueWidget(
                  valueController: TextEditingController(text: ""),
                  label: AppLocalizations.of(context)!.serialNo,
                  isValueEditable: false,
                  isValueReadOnly: true,
                  onValueTap: () {}),
            ],
          ),
        ),
      ),
    );
  }

  _getGeneralCard(FUNC_LOC_HEADER? funcLocHeader) {
    if (PlatformDetails.isMobileScreen(context) ||
        PlatformDetails.isTabPortraitScreen(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GeneralTitleText(
            title: AppLocalizations.of(context)!.generalData,
          ),
          SizedBox(
            height: 16,
          ),
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.description,
              subtitle:
                  (funcLocHeader != null) ? funcLocHeader.shtxt ?? "" : "",
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.parentLocation,
              subtitle: (funcLocHeader != null &&
                      !Utils.isNullOrEmpty(funcLocHeader.super_func_loc_desc))
                  ? Utils.formatIdDescriptionWithhyphen(
                      id: funcLocHeader.super_func_loc,
                      description: funcLocHeader.super_func_loc_desc,
                    )
                  : AppLocalizations.of(context)!.na,
            ),
          ),
          SizedBox(
            height: 16,
          ),
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: "User Status",
              subtitle: (funcLocHeader != null)
                  ? funcLocHeader.user_status ?? ""
                  : "",
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: "System Status",
              subtitle: (funcLocHeader != null &&
                      !Utils.isNullOrEmpty(funcLocHeader.syst_stat))
                  ? funcLocHeader.syst_stat ?? ""
                  : AppLocalizations.of(context)!.na,
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GeneralTitleText(
          title: AppLocalizations.of(context)!.generalData,
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.description,
                  subtitle:
                      (funcLocHeader != null) ? funcLocHeader.shtxt ?? "" : "",
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.parentLocation,
                  subtitle: (funcLocHeader != null &&
                          !Utils.isNullOrEmpty(
                              funcLocHeader.super_func_loc_desc))
                      ? Utils.formatIdDescriptionWithhyphen(
                          id: funcLocHeader.super_func_loc,
                          description: funcLocHeader.super_func_loc_desc,
                        )
                      : AppLocalizations.of(context)!.na,
                ),
              ),
            ),
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: "User Status",
                  subtitle: (funcLocHeader != null)
                      ? funcLocHeader.user_status ?? ""
                      : "",
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: "System Status",
                  subtitle: (funcLocHeader != null &&
                          !Utils.isNullOrEmpty(funcLocHeader.syst_stat))
                      ? funcLocHeader.syst_stat ?? ""
                      : AppLocalizations.of(context)!.na,
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  _getGeneralCardContent(FUNC_LOC_HEADER? funcLocHeader) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LabelValueWidget(
            label: AppLocalizations.of(context)!.description,
            isValueEditable: false,
            isValueReadOnly: true,
            valueController: TextEditingController(
                text: (funcLocHeader != null) ? funcLocHeader.shtxt! : ""),
            onValueTap: () => {}),
        Visibility(
          visible: false,
          child: LabelValueWidget(
              label: AppLocalizations.of(context)!.objectType,
              isValueEditable: false,
              isValueReadOnly: true,
              valueController: TextEditingController(text: ""),
              onValueTap: () => {}),
        ),
        Visibility(
          visible: false,
          child: LabelValueWidget(
              label: AppLocalizations.of(context)!.functionalLocation,
              isValueEditable: false,
              isValueReadOnly: true,
              valueController: TextEditingController(text: ""),
              onValueTap: () => {}),
        ),
        LabelValueWidget(
            label: AppLocalizations.of(context)!.parentLocation,
            isValueEditable: false,
            isValueReadOnly: true,
            valueController: TextEditingController(
                text: (funcLocHeader != null &&
                        !Utils.isNullOrEmpty(funcLocHeader.super_func_loc_desc))
                    ? funcLocHeader.super_func_loc! +
                        " (${funcLocHeader.super_func_loc_desc})"
                    : ""),
            onValueTap: () => {}),
      ],
    );
  }

  Future<FUNC_LOC_HEADER?> _getFlocHeader({required String funcLoc}) async {
    FUNC_LOC_HEADER? func_loc_header =
        (await TechObjectsHelper.getFLocHeader(fLocVal: funcLoc));
    if (func_loc_header != null) {
      location = await TechObjectsHelper.getAddInfoLocationFLoc(
          fLoc: func_loc_header.func_loc!);
      org = await TechObjectsHelper.getAddInfoOrganizationFLoc(
          fLoc: func_loc_header.func_loc!);
    }
    return func_loc_header;
  }
  getOrganizationCard(
      BuildContext context, FUNCLOC_ORG_ADD? org, FUNCLOC_LOC_ADD? location) {
    if (PlatformDetails.isMobileScreen(context) ||
        PlatformDetails.isTabPortraitScreen(context)) {
      return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        GeneralTitleText(
          title: "Organization",
        ),
        SizedBox(
          height: 16,
        ),
        SizedBox(
          height: _cardHeight,
          child: GeneralItem(
            title: "Cost Center",
            subtitle: org != null
                ? Utils.formatIdDescriptionWithhyphen(
                    id: org.org_plng_plant,
                    description: org.org_plng_plant_desc,
                  )
                : AppLocalizations.of(context)!.na,
          ),
        ),
        ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
        SizedBox(
          height: _cardHeight,
          child: GeneralItem(
            title: "ABC Indicator",
            subtitle: location != null
                ? location.loc_abc ??
                    "" + " - " + location.loc_abc_desc.toString()
                : AppLocalizations.of(context)!.na,
          ),
        ),
      ]);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GeneralTitleText(
          title: "Organization",
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: "Cost Center",
                  subtitle: org != null
                      ? Utils.formatIdDescriptionWithhyphen(
                          id: org.org_plng_plant,
                          description: org.org_plng_plant_desc,
                        )
                      : AppLocalizations.of(context)!.na,
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: "ABC Indicator",
                  subtitle: location != null
                      ? Utils.formatIdDescriptionWithhyphen(
                          id: location.loc_abc,
                          description: location.loc_abc_desc,
                        )
                      : AppLocalizations.of(context)!.na,
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}
