import 'package:eam/be/EQUIP_TASKLIST.dart';
import 'package:eam/be/ORDER_TASKLIST_HEADER.dart';
import 'package:eam/helpers/db_helper.dart';
import 'package:eam/helpers/extensions.dart';
import 'package:eam/helpers/pa_helper.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/work_orderdetail_view.dart';
import 'package:eam/provider/order/add_order_form_provider.dart';
import 'package:eam/provider/tech_objects/equipment_details_provider.dart';
import 'package:eam/services/navigation_service.dart';
import 'package:eam/utils/screen_util.dart';
import 'package:eam/utils/utils.dart';
import 'package:eam/widgets/details_card_widget.dart';
import 'package:eam/widgets/label_value_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../../../helpers/platform_details.dart';
import '../../../../app_styles/app_styles.dart';
import '../../../../widgets/atoms_layer/general/texts.dart';
import '../../../../widgets/molecules_layer/general/general_helper_widgets.dart';

class EquipmentDetailGeneral extends StatefulWidget {
  final String equipNo;

  const EquipmentDetailGeneral({Key? key, required this.equipNo})
      : super(key: key);

  @override
  _EquipmentDetailGeneralState createState() => _EquipmentDetailGeneralState();
}

class _EquipmentDetailGeneralState extends State<EquipmentDetailGeneral> {
  late EquipmentDetailsProvider _equipmentDetailsProvider;
  bool isInitialized = false;

  static const double _cardHeight = 45;
  double _itemPadding = 20;
  double _betweenSpace = 24.0;

  final generalDivider = Divider(
    color: HexColor("#D5DADD"),
  );

  @override
  Widget build(BuildContext context) {
    _equipmentDetailsProvider = Provider.of<EquipmentDetailsProvider>(context);
    if (!isInitialized) {
      _equipmentDetailsProvider.init(equipNo: widget.equipNo);
      isInitialized = true;
    }

    return Scaffold(
      body: _equipmentDetailsProvider.isFetching
          ? Center(
              child: CircularProgressIndicator(),
            )
          : SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                    decoration: AppStyles.deFaultBoxDecoration,
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: _itemPadding,
                          right: _itemPadding,
                          top: _itemPadding,
                          bottom: _itemPadding),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _getGeneralCard(context),
                          if (_equipmentDetailsProvider.equipManufacturer !=
                              null) ...[
                            _betweenSpace.spaceY,
                            generalDivider,
                            _betweenSpace.spaceY,
                            _getManufacturerCard(context),
                          ],
                          _betweenSpace.spaceY,
                          generalDivider,
                          _betweenSpace.spaceY,
                          _getLocationCard(context),
                          _betweenSpace.spaceY,
                          generalDivider,
                          _betweenSpace.spaceY,
                          _getResponsibilityCard(context),
                          _betweenSpace.spaceY,
                          generalDivider,
                          _betweenSpace.spaceY,
                          _getTaskList(),
                        ],
                      ),
                    ),
                  ),
                  25.0.spaceY
                ],
              ),
            ),
    );
  }

  _getResponsibilityCard(BuildContext context) {
    if (PlatformDetails.isMobileScreen(context) ||
        PlatformDetails.isTabPortraitScreen(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GeneralTitleText(
            title: AppLocalizations.of(context)!.responsibility,
          ),
          SizedBox(
            height: 16,
          ),
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.planningPlant,
              subtitle: _getPlanningPlantText(),
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.plannerGroup,
              subtitle: _getPlannerGroupText(),
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.mainWorkCenter,
              subtitle: _getWorkcenterText(),
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.catalogProfile,
              subtitle: _getCatalogProfileText(),
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GeneralTitleText(
          title: AppLocalizations.of(context)!.responsibility,
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.planningPlant,
                  subtitle: _getPlanningPlantText(),
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.plannerGroup,
                  subtitle: _getPlannerGroupText(),
                ),
              ),
            ),
          ],
        ),
        ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.mainWorkCenter,
                  subtitle: _getWorkcenterText(),
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.catalogProfile,
                  subtitle: _getCatalogProfileText(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getWorkcenterText() {
    final desc = _equipmentDetailsProvider.equipHeader?.org_main_wrk_cntr_desc;
    final code = _equipmentDetailsProvider.equipHeader?.org_main_wrk_cntr;

    if (!Utils.isNullOrEmpty(desc) && !Utils.isNullOrEmpty(code)) {
      return '$desc - $code';
    } else if (!Utils.isNullOrEmpty(desc)) {
      return desc ?? "";
    } else if (!Utils.isNullOrEmpty(code)) {
      return code.toString();
    } else {
      return ''; // or any other default value you prefer
    }
  }

  String _getCatalogProfileText() {
    final desc = _equipmentDetailsProvider.equipHeader?.org_catlg_profl_desc;
    final code = _equipmentDetailsProvider.equipHeader?.org_catlg_profl;

    if (!Utils.isNullOrEmpty(desc) && !Utils.isNullOrEmpty(code)) {
      return '$desc - $code';
    } else if (!Utils.isNullOrEmpty(desc)) {
      return desc ?? "";
    } else if (!Utils.isNullOrEmpty(code)) {
      return code.toString();
    } else {
      return ''; // or any other default value you prefer
    }
  }

  String _getPlannerGroupText() {
    final desc = _equipmentDetailsProvider.org?.org_plnr_grp_desc;
    final code = _equipmentDetailsProvider.org?.org_plnr_grp;

    if (!Utils.isNullOrEmpty(desc) && !Utils.isNullOrEmpty(code)) {
      return '$desc - $code';
    } else if (!Utils.isNullOrEmpty(desc)) {
      return desc ?? "";
    } else if (!Utils.isNullOrEmpty(code)) {
      return code.toString();
    } else {
      return ''; // or any other default value you prefer
    }
  }

  String _getPlanningPlantText() {
    final desc = _equipmentDetailsProvider.org?.org_plng_plant_desc;
    final code = _equipmentDetailsProvider.org?.org_plng_plant;

    if (!Utils.isNullOrEmpty(desc) && !Utils.isNullOrEmpty(code)) {
      return '$desc - $code';
    } else if (!Utils.isNullOrEmpty(desc)) {
      return desc ?? "";
    } else if (!Utils.isNullOrEmpty(code)) {
      return code.toString();
    } else {
      return ''; // or any other default value you prefer
    }
  }

  _getLocationCard(BuildContext context) {
    if (PlatformDetails.isMobileScreen(context) ||
        PlatformDetails.isTabPortraitScreen(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GeneralTitleText(
            title: AppLocalizations.of(context)!.location,
          ),
          SizedBox(
            height: 16,
          ),
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.maintenancePlant,
              subtitle: _equipmentDetailsProvider.equipHeader != null
                  ? _equipmentDetailsProvider.equipHeader!.loc_main_plant ??
                      "" +
                          " - " +
                          _equipmentDetailsProvider
                              .equipHeader!.loc_main_plant_desc
                              .toString()
                  : "",
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.plantSection,
              subtitle: _equipmentDetailsProvider.location != null
                  ? _equipmentDetailsProvider.location!.loc_plant_sec ??
                      "" +
                          (_equipmentDetailsProvider
                                  .location!.loc_plant_sec_desc!.isNotEmpty
                              ? _equipmentDetailsProvider
                                      .location!.loc_plant_sec ??
                                  "" +
                                      " - " +
                                      _equipmentDetailsProvider
                                          .location!.loc_plant_sec_desc
                                          .toString()
                              : '')
                  : '',
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.sortField,
              subtitle: _equipmentDetailsProvider.equipHeader != null
                  ? _equipmentDetailsProvider.equipHeader!.loc_sort_field
                      .toString()
                  : "",
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GeneralTitleText(
          title: AppLocalizations.of(context)!.location,
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.maintenancePlant,
                  subtitle: _equipmentDetailsProvider.equipHeader != null
                      ? _equipmentDetailsProvider.equipHeader!.loc_main_plant ??
                          "" +
                              " - " +
                              _equipmentDetailsProvider
                                  .equipHeader!.loc_main_plant_desc
                                  .toString()
                      : "",
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.plantSection,
                  subtitle: _equipmentDetailsProvider.location != null
                      ? _equipmentDetailsProvider.location!.loc_plant_sec ??
                          "" +
                              (_equipmentDetailsProvider
                                      .location!.loc_plant_sec_desc!.isNotEmpty
                                  ? _equipmentDetailsProvider
                                          .location!.loc_plant_sec ??
                                      "" +
                                          " - " +
                                          _equipmentDetailsProvider
                                              .location!.loc_plant_sec_desc
                                              .toString()
                                  : '')
                      : '',
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  _getManufacturerCard(BuildContext context) {
    if (PlatformDetails.isMobileScreen(context) ||
        PlatformDetails.isTabPortraitScreen(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GeneralTitleText(
            title: AppLocalizations.of(context)!.manufacturer,
          ),
          SizedBox(
            height: 16,
          ),
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.name,
              subtitle:
                  _equipmentDetailsProvider.equipManufacturer!.mfg_mfctr ??
                      AppLocalizations.of(context)!.na,
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.modelNo,
              subtitle:
                  _equipmentDetailsProvider.equipManufacturer!.mfg_model_no ??
                      AppLocalizations.of(context)!.na,
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.partNo,
              subtitle: _equipmentDetailsProvider
                      .equipManufacturer!.mfg_mfctr_part_no ??
                  AppLocalizations.of(context)!.na,
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.serialNo,
              subtitle: _equipmentDetailsProvider
                      .equipManufacturer!.mfg_mfctr_serial_no ??
                  AppLocalizations.of(context)!.na,
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GeneralTitleText(
          title: AppLocalizations.of(context)!.manufacturer,
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.name,
                  subtitle:
                      _equipmentDetailsProvider.equipManufacturer!.mfg_mfctr ??
                          AppLocalizations.of(context)!.na,
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.modelNo,
                  subtitle: _equipmentDetailsProvider
                          .equipManufacturer!.mfg_model_no ??
                      AppLocalizations.of(context)!.na,
                ),
              ),
            ),
          ],
        ),
        ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.partNo,
                  subtitle: _equipmentDetailsProvider
                          .equipManufacturer!.mfg_mfctr_part_no ??
                      AppLocalizations.of(context)!.na,
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.serialNo,
                  subtitle: _equipmentDetailsProvider
                          .equipManufacturer!.mfg_mfctr_serial_no ??
                      AppLocalizations.of(context)!.na,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  _getGeneralCard(BuildContext context) {
    if (PlatformDetails.isMobileScreen(context) ||
        PlatformDetails.isTabPortraitScreen(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GeneralTitleText(
            title: AppLocalizations.of(context)!.generalData,
          ),
          SizedBox(
            height: 16,
          ),
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.description,
              subtitle: Utils.isNullOrEmpty(
                      _equipmentDetailsProvider.equipHeader!.shtxt)
                  ? ""
                  : _equipmentDetailsProvider.equipHeader!.shtxt!,
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
              title: AppLocalizations.of(context)!.objectType,
              subtitle: Utils.isNullOrEmpty(_equipmentDetailsProvider
                      .equipHeader!.gen_object_type_text)
                  ? ""
                  : Utils.isNullOrEmpty(_equipmentDetailsProvider
                          .equipHeader!.gen_object_type)
                      ? ""
                      : _equipmentDetailsProvider
                              .equipHeader!.gen_object_type! +
                          " - " +
                          _equipmentDetailsProvider
                              .equipHeader!.gen_object_type_text!,
            ),
          ),
          ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
          SizedBox(
            height: _cardHeight,
            child: GeneralItem(
                title: AppLocalizations.of(context)!.functionalLocation,

                // subtitle: (_equipmentDetailsProvider.equipHeader != null &&
                //         !Utils.isNullOrEmpty(_equipmentDetailsProvider
                //             .equipHeader!.super_func_loc_desc))
                //     ? _equipmentDetailsProvider.equipHeader!.super_func_loc! +
                //         " (${_equipmentDetailsProvider.equipHeader!.super_func_loc_desc})"
                //     : AppLocalizations.of(context)!.na,

                subtitle: Utils.isNullOrEmpty(
                        _equipmentDetailsProvider.equipHeader!.super_func_loc)
                    ? ""
                    : _equipmentDetailsProvider.equipHeader!.super_func_loc! +
                        "${Utils.isNullOrEmpty(_equipmentDetailsProvider.equipHeader!.super_func_loc_desc) ? "" : "(${_equipmentDetailsProvider.equipHeader!.super_func_loc_desc})"}"

                //  subtitle:
                //     ("${_equipmentDetailsProvider.equipHeader!.super_func_loc!} - ${_equipmentDetailsProvider.equipHeader!.super_func_loc_desc}"),
                ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GeneralTitleText(
          title: AppLocalizations.of(context)!.generalData,
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.description,
                  subtitle: _equipmentDetailsProvider.equipHeader!.shtxt!,
                ),
              ),
            ),
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.objectType,
                  subtitle: Utils.isNullOrEmpty(_equipmentDetailsProvider
                          .equipHeader!.gen_object_type_text)
                      ? ""
                      : Utils.isNullOrEmpty(_equipmentDetailsProvider
                              .equipHeader!.gen_object_type)
                          ? ""
                          : _equipmentDetailsProvider
                                  .equipHeader!.gen_object_type! +
                              " - " +
                              _equipmentDetailsProvider
                                  .equipHeader!.gen_object_type_text!,
                  //   subtitle: Utils.isNullOrEmpty(_equipmentDetailsProvider
                  //     .equipHeader!.gen_object_type_text!)
                  // ? ""
                  // : CheckNullString(_equipmentDetailsProvider
                  //         .equipHeader!.gen_object_type)    +
                  //     " - " +
                  //     CheckNullString(  _equipmentDetailsProvider
                  //         .equipHeader!.gen_object_type_text!)
                  //   ,
                  // subtitle: Utils.isNullOrEmpty(_equipmentDetailsProvider
                  //         .equipHeader!.gen_object_type_text!)
                  //     ? ""
                  //     : _equipmentDetailsProvider
                  //             .equipHeader!.gen_object_type! +
                  //         " - " +
                  //         _equipmentDetailsProvider
                  //             .equipHeader!.gen_object_type_text!,
                ),
              ),
            ),
          ],
        ),
        ScreenUtils.GENERAL_SPACE_VALUE.spaceY,
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: _cardHeight,
                child: GeneralItem(
                  title: AppLocalizations.of(context)!.functionalLocation,
                  subtitle: _equipmentDetailsProvider.equipHeader!.super_func_loc!,
                ),
              ),
            ),
            Expanded(child: GeneralItem(
              title: "Construction Type",
              subtitle: _equipmentDetailsProvider.equipHeader!.constr_type_material!,
            )),
          ],
        ),
        // SizedBox(
        //   height: _cardHeight,
        //   child: GeneralItem(
        //     title: AppLocalizations.of(context)!.functionalLocation,
        //     subtitle:
        //         _equipmentDetailsProvider.equipHeader!.super_func_loc_desc!,
        //   ),
        // ),
      ],
    );

    return DetailsCardWidget(
      isEditable: false,
      title: AppLocalizations.of(context)!.generalData,
      content: Container(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LabelValueWidget(
                label: AppLocalizations.of(context)!.description,
                isValueEditable: false,
                isValueReadOnly: true,
                valueController: TextEditingController(
                    text: _equipmentDetailsProvider.equipHeader!.shtxt!),
                onValueTap: () => {}),
            LabelValueWidget(
                label: AppLocalizations.of(context)!.objectType,
                isValueEditable: false,
                isValueReadOnly: true,
                valueController: TextEditingController(
                    text: Utils.isNullOrEmpty(_equipmentDetailsProvider
                            .equipHeader!.gen_object_type_text!)
                        ? ""
                        : _equipmentDetailsProvider
                                .equipHeader!.gen_object_type! +
                            " - " +
                            _equipmentDetailsProvider
                                .equipHeader!.gen_object_type_text!),
                onValueTap: () => {}),
            LabelValueWidget(
                label: AppLocalizations.of(context)!.functionalLocation,
                isValueEditable: false,
                isValueReadOnly: true,
                valueController: TextEditingController(
                    text: _equipmentDetailsProvider
                        .equipHeader!.super_func_loc_desc!),
                onValueTap: () => {}),
            Visibility(
              visible: false,
              child: LabelValueWidget(
                  label: AppLocalizations.of(context)!.parentLocation,
                  isValueEditable: false,
                  isValueReadOnly: true,
                  valueController: TextEditingController(text: ""),
                  onValueTap: () => {}),
            ),
          ],
        ),
      ),
    );
  }

  _getTaskList_old() {
    return _equipmentDetailsProvider.equipmentTaskList.isNotEmpty
        ? DetailsCardWidget(
            isEditable: false,
            title: AppLocalizations.of(context)!.taskList,
            content: Container(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: _equipmentDetailsProvider.equipmentTaskList
                    .map(
                      (equipTask) => _getSingleTaskItem(equipTask: equipTask),
                    )
                    .toList(),
              ),
            ),
          )
        : SizedBox();
  }

  _getTaskList() {
    return _equipmentDetailsProvider.equipmentTaskList.isNotEmpty
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GeneralTitleText(
                title: context.locale.taskList,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: _equipmentDetailsProvider.equipmentTaskList
                    .map(
                      (equipTask) => _getSingleTaskItem(equipTask: equipTask),
                    )
                    .toList(),
              ),
            ],
          )
        : SizedBox();
  }

  _createNewOrder({required EQUIP_TASKLIST equipTask}) async {
    if (!(await PAHelper.isSyncAllowed())) {
      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.systemIsBusyString);
      return;
    }
    try {
      String userId = await Utils.getUserId();
      String? lan = await DbHelper.getLanguage();
      ORDER_TASKLIST_HEADER orderTaskListHeader = ORDER_TASKLIST_HEADER(
        counter: equipTask.counter,
        equip_id: equipTask.equnr,
        tasklist_type: equipTask.tasklist_type,
        task_group: equipTask.task_group,
        language: lan ?? "en",
        user_id: userId,
      );
      Map<String, dynamic> data = {
        "ORDER_TASKLIST": [
          {"ORDER_TASKLIST_HEADER": orderTaskListHeader.toJson()}
        ]
      };
      await createOrderInSync(data: data);
    } catch (e) {
      Logger.logError('sourceClass', '_createNewOrder', e.toString());
    }
  }

  Future<void> createOrderInSync({required Map<String, dynamic> data}) async {
    UIHelper.showEamProgressDialog(
      context,
      title: AppLocalizations.of(context)!.pleaseWaitWhileCreatingNewOrder,
      showCancelIcon: false,
      barrierDismissible: false,
    );
    Result result = await PAHelper.createNewOrderFromOrderTask(
      data: data,
    );
    if (result.statusCode == Status.httpBadRequest ||
        result.statusCode == Status.httpNotFound) {
      // Navigator.of(context, rootNavigator: true).pop();
      UIHelper.closeDialog(context);
      if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
        UIHelper.showEamDialog(
          context,
          title: AppLocalizations.of(context)!.alertString,
          description: result.body['InfoMessage'][0]['message'],
          dismissible: false,
          positiveActionLabel: AppLocalizations.of(context)!.okayString,
          onPositiveClickListener: () => UIHelper.closeDialog(context),
        );
      }
    } else if (result.statusCode == Status.httpOk ||
        result.statusCode == Status.httpCreated) {
      await context.read<OrderNotifier>().getOrders(context);
      Navigator.of(context, rootNavigator: true).pop();
      print(result.body);
      if (result.body['InfoMessage'] != null) {
        if (result.body['InfoMessage'][0]['category'] == 'FAILURE') {
          UIHelper.showEamDialog(
            context,
            title: AppLocalizations.of(context)!.alertString,
            description: result.body['InfoMessage'][0]['message'],
            dismissible: false,
            positiveActionLabel: AppLocalizations.of(context)!.okayString,
            onPositiveClickListener: () => UIHelper.closeDialog(context),
          );
        } else {
          String orderNo = result.body["ORDER"][0]["ORDER_HEADER"]["ORDER_NO"];

          UIHelper.showEamDialog(
            context,
            title: AppLocalizations.of(context)!.infoString,
            description: "You order has been created with number ${orderNo}",
            dismissible: false,
            positiveActionLabel: AppLocalizations.of(context)!.okayString,
            onPositiveClickListener: () {
              Navigator.of(context).pop();
              NavigationService.pushNamed(
                WorkOrderDetailView.routeName,
                arguments: OrderDetailArguments(
                    isNewOrder: false, orderName: "", orderNumber: orderNo),
              );
            },
          );
        }
      } else {
        print(result.body);
        String orderNo = result.body["ORDER"][0]["ORDER_HEADER"]["ORDER_NO"];
        String orderName = result.body["ORDER"][0]["ORDER_HEADER"]["SHORT_TXT"];
        print(orderNo);
        print(orderName);

        await context
            .read<AddOrderFormProvider>()
            .loadFormList(orderNo: orderNo, context: context, formAuto: true);

        UIHelper.showEamDialog(
          context,
          title: AppLocalizations.of(context)!.infoString,
          description: AppLocalizations.of(context)!.orderCreatedSuccessfully,
          positiveActionLabel: AppLocalizations.of(context)!.okayString,
          onPositiveClickListener: () {
            //Provider.of<OrderProvider>(context).getOrderList(context);
            Navigator.of(context, rootNavigator: true).pop();
            _gotoOrderDetail(name: orderName, orderNumber: orderNo);
            /*Navigator.of(context).pushNamed(OrderDetailPage.routeName,
                arguments: {OrderDetailPage.ORDER_NO: "orderNo"});*/
          },
        );
        //Navigator.pop(context);
      }

      //setState(() {});
    }
  }

  _gotoOrderDetail({
    required String name,
    required String orderNumber,
  }) {
    NavigationService.pushNamed(WorkOrderDetailView.routeName,
        arguments: OrderDetailArguments(
            isNewOrder: false, orderName: name, orderNumber: orderNumber));
  }

  Widget _getSingleTaskItem({required EQUIP_TASKLIST equipTask}) {
    return Column(
      children: [
        12.0.spaceY,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                //equipTask.mnt_plan_desc ?? '',
                equipTask.mnt_plan_desc ?? '',
                //style: ScreenUtils.getLabelTextStyle(),
                style: AppStyles.textStyle_14_500w,
                maxLines: 1,
              ),
            ),
            OutlinedButton(
              onPressed: () => _createNewOrder(equipTask: equipTask),
              child: Text(AppLocalizations.of(context)!.createOrder),
            ),
          ],
        ),
      ],
    );
  }
}

CheckNullString(String? value) {
  if (value != null && value.isNotEmpty) {
    return value;
  }
  return "";
}
