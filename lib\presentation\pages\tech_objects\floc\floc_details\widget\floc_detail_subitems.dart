import 'package:eam/be/EQUIP_HEADER.dart';
import 'package:eam/be/EQUIP_INPUT_HEADER.dart';
import 'package:eam/be/FUNCLOC_SUB_EQUIP.dart';
import 'package:eam/be/FUNCLOC_SUB_LOC.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/be/FUNC_LOC_INPUT_HEADER.dart';
import 'package:eam/helpers/tech_objects_helper.dart';
import 'package:eam/presentation/common_widgets/inkwell.dart';
import 'package:eam/provider/tech_objects/floc_details_provider.dart';
import 'package:eam/screens/param/route_param.dart';
import 'package:eam/presentation/pages/tech_objects/equipments/equipment_details_page.dart';
import 'package:eam/presentation/pages/tech_objects/floc/floc_details/floc_details_page.dart';
import 'package:eam/utils/utils.dart';
import 'package:eam/widgets/common_widget.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';

import '../../../../../app_styles/app_styles.dart';
import '../../../../../common_widgets/eam_tab.dart';
import '../../../../../widgets/atoms_layer/eam_icons.dart';

class FlocDetailSubItems extends StatefulWidget {
  final String funLoc;

  const FlocDetailSubItems({Key? key, required this.funLoc}) : super(key: key);

  @override
  _FlocDetailSubItemsState createState() => _FlocDetailSubItemsState();
}

class _FlocDetailSubItemsState extends State<FlocDetailSubItems> {
  int selectedIndex = 0;
  late FlocDetailsProvider _flocDetailsProvider;
  bool isInitialized = false;
  List _tabItems = [];

  @override
  void initState() {
    // TODO: implement initState
    Provider.of<FlocDetailsProvider>(context, listen: false)
        .loadEquipmentList(funcLoc: widget.funLoc);
    Provider.of<FlocDetailsProvider>(context, listen: false)
        .loadFunctionalLocationList(funcLoc: widget.funLoc);
    super.initState();
  }

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    _flocDetailsProvider = Provider.of<FlocDetailsProvider>(context);
    if (!isInitialized) {
      _flocDetailsProvider.loadEquipmentList(funcLoc: widget.funLoc);
      isInitialized = true;
    }
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _tabs(),
          Expanded(
              child: _flocDetailsProvider.isFetching
                  ? Center(
                      child: CircularProgressIndicator(),
                    )
                  : selectedIndex == 0
                      ? _getSubEquipment()
                      : _getSubLocation())
        ],
      ),
    );
  }

  _getSubEquipment() {
    if (_flocDetailsProvider.subEquipmentList.length == 0) {
      return EamNoRecordWidget();
    }

    return ListView.separated(
      padding: EdgeInsets.only(
          bottom: kToolbarHeight + 15, right: 5, left: 5, top: 5),
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 5,
        );
      },
      itemBuilder: (context, index) {
        return buildEquipmentItem(
            subEquip: _flocDetailsProvider.subEquipmentList[index]);
      },
      itemCount: _flocDetailsProvider.subEquipmentList.length,
    );
  }

  _getSubLocation() {
    if (_flocDetailsProvider.subFLocList.length == 0) {
      return EamNoRecordWidget();
    }
    return ListView.separated(
      padding: EdgeInsets.only(
          bottom: kToolbarHeight + 15, right: 5, left: 5, top: 5),
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 5,
        );
      },
      itemBuilder: (context, index) {
        return buildSubFlocItem(
            subFloc: _flocDetailsProvider.subFLocList[index]);
      },
      itemCount: _flocDetailsProvider.subFLocList.length,
    );
  }

  buildEquipmentItem({required FUNCLOC_SUB_EQUIP subEquip}) {
    return Container(
      decoration: AppStyles.deFaultBoxDecoration,
      child: InkWellCard(
        borderRadius: 8,
        onTap: () async {
          bool? existsInDb = await TechObjectsHelper.isSubEquipmentExistsInDB(
              equipNo: subEquip.sub_equnr!);
          if (existsInDb!) {
            _navigateToEquipmentDetails(subEquip: subEquip);
          } else {
            downloadEquipment(
                context: context,
                equipNo: subEquip.sub_equnr!,
                fromSubItems: true,
                successCallback: () {
                  _navigateToEquipmentDetails(subEquip: subEquip);
                });
          }
        },
        child: ListTile(
          leading: EamIcon(iconName: EamIcon.equip).icon(),
          title: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 8,
                    ),
                    _getEquipTitle(subEquip),
                    SizedBox(
                      height: 8,
                    ),
                    _getEquipSubTitle(subEquip),
                    SizedBox(
                      height: 8,
                    ),
                  ],
                ),
              ),
              Expanded(child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "${subEquip.syncStatus.name}",
                    style: AppStyles.textStyle_14_700w.copyWith(
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text(
                    "",
                    style: AppStyles.textStyle_14_500w.copyWith(
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ))
            ],
          ),
          trailing:
              EamIcon(iconName: EamIcon.arrow_forward, height: 16, width: 16)
                  .icon(),
        ),
      ),
    );
  }

  buildSubFlocItem({required FUNCLOC_SUB_LOC subFloc}) {
    return Container(
      decoration: AppStyles.deFaultBoxDecoration,
      child: InkWellCard(
        borderRadius: 8,
        onTap: () async {
          bool? existsInDb =
              await TechObjectsHelper.isSubFunctionalLocExistsInDB(
                  subFLoc: subFloc.sub_func_loc!);
          if (existsInDb!) {
            _navigateToFunctionalLocDetails(subFLocList: subFloc);
          } else {
            downloadFunctionalLocation(
                context: context,
                funcLocation: subFloc.sub_func_loc!,
                fromSubItems: true,
                successCallback: () {
                  _navigateToFunctionalLocDetails(subFLocList: subFloc);
                });
          }
        },
        child: ListTile(
          leading: EamIcon(iconName: EamIcon.equip).icon(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 8,
              ),
              _getFlocTitle(subFloc: subFloc),
              SizedBox(
                height: 8,
              ),
              _getFlocSubTitle(subFloc: subFloc),
              SizedBox(
                height: 8,
              ),
            ],
          ),
          trailing:
              EamIcon(iconName: EamIcon.arrow_forward, height: 16, width: 16)
                  .icon(),
        ),
      ),
    );
  }

  Future<void> _navigateToFunctionalLocDetails(
      {required FUNCLOC_SUB_LOC subFLocList}) async {
    FUNC_LOC_HEADER? funcLocHeader = await TechObjectsHelper.getFLocHeader(
        fLocVal: subFLocList.sub_func_loc!);

    Navigator.of(context).pushNamed(FlocDetailsPage.routeName,
        arguments: funcLocHeader!.func_loc!);
  }

  downloadFunctionalLocation(
      {required BuildContext context,
      required String funcLocation,
      required bool fromSubItems,
      required Function successCallback}) async {
    try {
      FUNC_LOC_INPUT_HEADER funcLocInputHeader =
          new FUNC_LOC_INPUT_HEADER(fl_val: funcLocation);
      // funcLocInputHeader.fl_val = funcLocation;
      await _flocDetailsProvider.downloadData(
          fromSubItems: fromSubItems,
          equipMode: false,
          downloadMaterial: false,
          context: context,
          successCallback: successCallback,
          funcLocInputHeader: funcLocInputHeader);
    } catch (e) {
      Logger.logError(
          "FlocDetailSubItems", 'downloadFunctionalLocation', e.toString());
    }
  }

  downloadEquipment(
      {required BuildContext context,
      required String equipNo,
      required bool fromSubItems,
      required Function successCallback}) async {
    try {
      EQUIP_INPUT_HEADER equipInputHeader =
          new EQUIP_INPUT_HEADER(equip_no: equipNo);
      // equipInputHeader.equip_no = equipNo;
      await _flocDetailsProvider.downloadData(
          fromSubItems: fromSubItems,
          equipMode: true,
          downloadMaterial: false,
          context: context,
          successCallback: successCallback,
          equipInputHeader: equipInputHeader);
    } catch (e) {
      Logger.logError(
          "FlocDetailSubItems", 'downloadFunctionalLocation', e.toString());
    }
  }

  void _navigateToEquipmentDetails(
      {required FUNCLOC_SUB_EQUIP subEquip}) async {
    EQUIP_HEADER? equip_header =
        await TechObjectsHelper.getEquipmentHeaderForDetail(
            equipNo: subEquip.sub_equnr!);

    Navigator.of(context).pushNamed(
      EquipmentDetailsPage.routeName,
      arguments: EquipmentParam(eqno: equip_header!.equnr!),
    );
  }

  _getEquipTitle(FUNCLOC_SUB_EQUIP subEquip) {
    return Visibility(
        visible: !Utils.isNullOrEmpty(subEquip.sub_equnr),
        child: Text(subEquip.sub_equnr!,
            style: AppStyles.textStyle_14_700w.copyWith(
              overflow: TextOverflow.ellipsis,
            )));
  }

  _getEquipSubTitle(FUNCLOC_SUB_EQUIP subEquip) {
    return Visibility(
        visible: !Utils.isNullOrEmpty(subEquip.shtxt),
        child: Text(subEquip.shtxt!,
            style: AppStyles.textStyle_14_500w.copyWith(
              overflow: TextOverflow.ellipsis,
            )));
  }

  _getFlocTitle({required FUNCLOC_SUB_LOC subFloc}) {
    return Visibility(
        visible: !Utils.isNullOrEmpty(subFloc.sub_func_loc),
        child: Text(subFloc.sub_func_loc!,
            style: AppStyles.textStyle_14_700w.copyWith(
              overflow: TextOverflow.ellipsis,
            )));
  }

  _getFlocSubTitle({required FUNCLOC_SUB_LOC subFloc}) {
    return Visibility(
        visible: !Utils.isNullOrEmpty(subFloc.shtxt),
        child: Text(subFloc.shtxt!,
            style: AppStyles.textStyle_14_500w.copyWith(
              overflow: TextOverflow.ellipsis,
            )));
  }

  _tabs() {
    _tabItems = [
      "Equipment (${_flocDetailsProvider.subEquipmentList.length})",
      "Floc (${_flocDetailsProvider.subFLocList.length})"
    ];
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 5),
      child: EamTabs(
        items: _tabItems,
        onChanged: (i, status) {
          setState(() {
            selectedIndex = i;
            if (selectedIndex == 0) {
              _flocDetailsProvider.loadEquipmentList(funcLoc: widget.funLoc);
            } else {
              _flocDetailsProvider.loadFunctionalLocationList(
                  funcLoc: widget.funLoc);
            }
          });
        },
      ),
    );
  }
}
